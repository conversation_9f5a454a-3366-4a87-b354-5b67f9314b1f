@model IEnumerable<OnlineKeFu>
    @{
    var cdn = CDN.GetCDN();
    }
    <div class="fixed-bar">
        <div class="wide-bar">
            <div class="consult-box">
                <div class="consult-header clearfix">
                    <h3 class="consult-title">@T("在线客服")<span class="icon-times-circle-o"><img
                                src="@(CDN.GetCDN())/images/x.png" alt="Alternate Text" /></span></h3>
                </div>
                <ul class="consult-list">
                    <li class="clearfix">
                        <a id="open-ai-service" href="javascript:;" lay-on="test-iframe-handle">
                            <img style="width: 28px;height: 28px;" border="0" src="@(cdn)/images/kffloat.png" alt="AI"
                                title="点击开始AI聊天">
                            <span>@T("deepseek客服")</span>
                        </a>
                    </li>
                    @foreach (var item in Model)
                    {
                    if (item.Location != 0 && item.Location != 1) continue; //非平台下客服不加载
                    switch (item.OType)
                    {
                    case 0:
                    <li class="clearfix">
                        <a target="_blank"
                            href="http://wpa.qq.com/msgrd?v=3&amp;uin=@(item.ONumber)&amp;site=qq&amp;menu=yes">
                            <img border="0" src="@(cdn)/images/JS_qq.png" alt="QQ" title="点击开始QQ交谈/留言">
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 1:
                    <li class="clearfix">
                        <a target="_blank"
                            href="https://www.taobao.com/go/market/webww/ww.php?ver=3&amp;touid=@(item.ONumber)&amp;siteid=cntaobao&amp;status=1&amp;charset=utf-8">
                            <img border="0" src="@(cdn)/images/ww.gif" alt="旺旺" title="点击开始旺旺交谈/留言">
                        </a>
                    </li>
                    break;

                    case 2:
                    <li class="clearfix">
                        <a rel="nofollow" href="skype:@(item.ONumber)?chat" target="_blank">
                            <img src="@(cdn)/images/skype.png" alt="Skype" title="点击开始Skype交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;

                    case 3:
                    var number = item.ONumber?.TrimStart("+").Replace(" ", "").Trim() ?? "";
                    //Whatsapp
                    <li class="clearfix">
                        <a rel="nofollow" href="https://api.whatsapp.com/send?phone=@(number)&text=Hello">
                            <img style="width:20px;height:20px;" src="@(cdn)/images/whatsapp.png" alt="Whatsapp"
                                title="点击开始Whatsapp交谈/留言" />
                            <span class="margin-small-left">@($"{item.OName}")</span>
                        </a>
                    </li>
                    break;
                    }
                    }
                </ul>
            </div>
        </div>
    </div>


<link href="~/static/plugins/js/layui/css/layui.css" rel="stylesheet"/>
    <script src="~/static/plugins/js/layui/layui.js"></script>

    <style>
        .layui-layer-title {
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            justify-content: space-between;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }
        .layui-layer-title {
            background: #dbdbdb;
            color: #000;
            font-size: 20px;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        .layui-layer-content {
            padding: 0;
        }
        #layui-layer-iframe1,
        #layui-layer-iframe2,
        #layui-layer-iframe3,
        #layui-layer-iframe4,
        #layui-layer-iframe5,
        #layui-layer-iframe6,
        #layui-layer-iframe7,
        #layui-layer-iframe8 {
            width: 100%;
        }
        .layui-layer-iframe {
            position: fixed !important;
            background-color: #eeeeee !important;
            border-radius: 10px !important;
        }

        /* 确保遮罩层显示 */
        .layui-layer-shade {
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            position: fixed;
        }
        .layui-layer-btn {
            justify-content: center;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }
         @* deepseek客服右上角关闭按钮 *@
        .layui-layer-btn a{
            text-decoration: none !important;
        }
       .layui-layer-btn0{
         position: absolute;
         top: 14px;
         right: 20px;
         cursor: pointer;
         font-weight: bolder
       }

    </style>
    <script asp-location="Footer">
        $(() => {
            $(".icon-times-circle-o").click(function () {
                $(this).parents().find(".fixed-bar").css("right", "-135px");
                $(".show-fixed-bar").addClass("comeOut");
            });
            $(".show-fixed-bar").click(() => {
                $(".icon-times-circle-o").parents().find(".fixed-bar").css("right", "0px");
                $(".show-fixed-bar").removeClass("comeOut");
            })
        })
        document.getElementById('open-ai-service').addEventListener('click', () => {
            layer.open({
                type: 2,
                title: '@T("deepseek客服")',
                area: ['500px', '700px'],
                shade: 0.6,  // 遮罩透明度和颜色
                shadeClose: true,
                btn: ['<i class="layui-icon layui-icon-close"></i>'],
                @* content: 'https://kf.hlktech.com/AIChat', *@
                content: 'http://localhost:9330/AIChat',
                success: function(layero, index) {
                    console.log('deepseek客服窗口加载成功');
                    // 打开了 实现监听第三方聊天窗口的回车事件 添加回车发送消息方法
                },
                end: function() {
                    console.log('deepseek客服客服窗口已关闭');
                }
            });
        })

    // 监听来自iframe的消息
    window.addEventListener('message', function (event) {
        console.log('📨 收到iframe消息:', event.data);

        // 允许的域名列表
        const allowedOrigins = [
            'https://kf.hlktech.com',
            'http://kf.hlktech.com',
            'https://ask.hlktech.com',
            'http://localhost:9330',  // 聊天项目开发环境
        ];

        if (!allowedOrigins.includes(event.origin)) {
            console.log('消息来源不被允许:', event.origin);
            return;
        }

        // 处理iframe加载完成消息
        if (event.data.type === 'iframeLoaded') {
            console.log('iframe已加载完成');
        }

        // 处理回车键按下消息
        if (event.data.type === 'ENTER_KEY_PRESSED') {
            console.log('iframe报告回车键被按下!',event.data.message);
        }



    });




</script>